# OldBrandName应用配置文件
app.name=OldBrandName
app.version=1.0.0
app.company=OldAppName Inc.

# API配置
api.base.url=https://old-api.example.com
api.timeout=30000
api.user.agent=OldAppName-Android/1.0

# 服务器配置
server.host=old-api.example.com
server.port=443
server.protocol=https

# 应用特性开关
feature.oldapp.enabled=true
feature.oldapp.debug=false
feature.oldbrand.analytics=true

# 第三方服务
analytics.provider=OldAppName Analytics
crash.reporting=OldBrandName Crashlytics
push.service=OldAppName Push

# 缓存配置
cache.oldapp.size=50MB
cache.oldapp.ttl=3600

# 日志配置
log.level=INFO
log.tag=OldBrandName
log.oldapp.enabled=true
