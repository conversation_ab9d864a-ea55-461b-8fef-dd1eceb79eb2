package com.example.utils

import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

/**
 * API客户端工具类
 * 用于OldBrandName应用的网络请求
 */
class ApiClient {
    
    companion object {
        private const val BASE_URL = "https://old-api.example.com/"
        private const val APP_NAME = "OldBrandName"
        private const val USER_AGENT = "OldAppName-Android/1.0"
        
        @Volatile
        private var INSTANCE: ApiClient? = null
        
        fun getInstance(): ApiClient {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ApiClient().also { INSTANCE = it }
            }
        }
    }
    
    private val okHttpClient: OkHttpClient by lazy {
        OkHttpClient.Builder()
            .addInterceptor { chain ->
                val request = chain.request().newBuilder()
                    .addHeader("User-Agent", USER_AGENT)
                    .addHeader("X-App-Name", APP_NAME)
                    .build()
                chain.proceed(request)
            }
            .build()
    }
    
    private val retrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    /**
     * 获取API服务
     */
    fun <T> createService(serviceClass: Class<T>): T {
        return retrofit.create(serviceClass)
    }
    
    /**
     * 获取完整的API地址
     */
    fun getFullApiUrl(endpoint: String): String {
        return "${BASE_URL}$endpoint"
    }
    
    /**
     * 记录API调用日志
     */
    private fun logApiCall(endpoint: String) {
        println("[$APP_NAME] API调用: ${getFullApiUrl(endpoint)}")
    }
    
    /**
     * OldBrandName专用的API配置
     */
    fun configureForOldBrandName() {
        println("正在为OldBrandName配置API客户端...")
        println("API地址: $BASE_URL")
        println("应用名称: $APP_NAME")
    }
}
