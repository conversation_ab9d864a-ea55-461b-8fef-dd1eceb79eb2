package com.example;

import android.app.Activity;
import android.os.Bundle;
import android.widget.TextView;

/**
 * 主活动类
 * 应用名称: OldBrandName
 */
public class MainActivity extends Activity {
    
    private static final String APP_NAME = "OldBrandName";
    private static final String API_URL = "https://old-api.example.com/api/v1";
    private static final String COMPANY_NAME = "OldAppName Inc.";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        TextView titleView = findViewById(R.id.title);
        titleView.setText("欢迎使用 " + APP_NAME);
        
        // 初始化应用
        initializeApp();
        
        // 设置API地址
        setupApiClient(API_URL);
    }
    
    private void initializeApp() {
        // OldBrandName 应用初始化逻辑
        System.out.println("正在初始化 " + APP_NAME + " 应用...");
        
        // 设置公司信息
        setCompanyInfo(COMPANY_NAME);
    }
    
    private void setupApiClient(String apiUrl) {
        // 配置API客户端
        System.out.println("配置API地址: " + apiUrl);
        
        // OldBrandName API配置
        configureOldBrandNameApi();
    }
    
    private void configureOldBrandNameApi() {
        // OldBrandName特定的API配置
        System.out.println("配置OldBrandName API...");
    }
    
    private void setCompanyInfo(String companyName) {
        System.out.println("公司名称: " + companyName);
        System.out.println("这是OldAppName公司的产品");
    }
    
    /**
     * 获取应用版本信息
     * @return OldBrandName应用版本
     */
    public String getAppVersion() {
        return "OldBrandName v1.0.0";
    }
}
