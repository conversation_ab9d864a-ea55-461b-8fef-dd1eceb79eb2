# Gradle配置文件
# 解决Java版本兼容性问题

# 设置Java版本
org.gradle.java.home=/System/Library/Frameworks/JavaVM.framework/Versions/Current/Commands/java
org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# 启用并行构建
org.gradle.parallel=true

# 启用配置缓存
org.gradle.configureondemand=true

# 启用构建缓存
org.gradle.caching=true

# Android相关配置
android.useAndroidX=true
android.enableJetifier=true

# 禁用不必要的检查以避免版本冲突
android.suppressUnsupportedCompileSdk=true
android.suppressUnsupportedOptionWarnings=true
