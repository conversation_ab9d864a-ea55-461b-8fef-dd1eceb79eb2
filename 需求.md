# Gradle字符串替换工具 - 详细需求文档

## 1. 项目概述

### 1.1 项目目标
开发一个Gradle脚本工具，用于在Android项目中批量搜索和替换字符串，并生成详细的替换报告。

### 1.2 应用场景
- 项目重构时的批量字符串替换
- 品牌名称或产品名称的统一修改
- 包名、类名的批量更新
- 配置参数的批量调整

## 2. 功能需求

### 2.1 核心功能
1. **多模块字符串搜索**
   - 搜索主模块（app module）中的所有字符串
   - 搜索所有子模块（library modules）中的字符串
   - 支持多种文件类型：.java、.kt、.xml、.properties等

2. **配置化字符串替换**
   - 通过外部配置文件定义替换规则
   - 支持多个替换规则同时执行
   - 支持正则表达式匹配（可选）

3. **详细替换报告**
   - 生成替换位置的详细信息
   - 包含类名、文件路径、行号
   - 替换前后的内容对比

### 2.2 配置要求
- **独立配置**：脚本可以轻松集成到任何Android项目
- **灵活配置**：通过配置文件管理替换规则
- **版本兼容**：兼容不同版本的Android Studio和Gradle

## 3. 技术规格

### 3.1 技术架构

#### 3.1.1 Gradle脚本结构
```
project-root/
├── build.gradle (主项目)
├── string-replacer/
│   ├── string-replacer.gradle (核心脚本)
│   ├── replacement-config.json (替换配置)
│   └── replacement-report.html (生成的报告)
└── modules/
    ├── app/
    └── library-modules/
```

#### 3.1.2 核心组件
1. **文件扫描器 (FileScanner)**
   - 递归扫描所有模块目录
   - 过滤指定文件类型
   - 排除build目录和其他临时文件

2. **字符串匹配器 (StringMatcher)**
   - 基于配置文件的匹配规则
   - 支持精确匹配和正则表达式
   - 记录匹配位置和上下文

3. **替换执行器 (ReplacementExecutor)**
   - 执行字符串替换操作
   - 备份原始文件
   - 记录替换详情

4. **报告生成器 (ReportGenerator)**
   - 生成HTML格式的详细报告
   - 包含替换统计信息
   - 提供回滚建议

### 3.2 配置文件格式

#### 3.2.1 replacement-config.json
```json
{
  "replacements": [
    {
      "id": "brand-name-update",
      "description": "更新品牌名称",
      "searchPattern": "OldBrandName",
      "replaceWith": "NewBrandName",
      "caseSensitive": true,
      "useRegex": false,
      "fileTypes": ["java", "kt", "xml"],
      "excludePaths": ["build/", "test/"]
    }
  ],
  "globalSettings": {
    "createBackup": true,
    "backupSuffix": ".backup",
    "reportFormat": "html",
    "logLevel": "info"
  }
}
```

### 3.3 集成方式

#### 3.3.1 项目级build.gradle集成
```gradle
// 在项目根目录的build.gradle中添加
apply from: 'string-replacer/string-replacer.gradle'
```

#### 3.3.2 任务定义
```gradle
task replaceStrings {
    group 'string-replacer'
    description '执行字符串替换操作'
    
    doLast {
        stringReplacer.execute()
    }
}
```

## 4. 使用流程

### 4.1 初始化配置
1. 将string-replacer目录复制到项目根目录
2. 在build.gradle中添加脚本引用
3. 根据需求修改replacement-config.json

### 4.2 执行替换
1. 运行预览任务：`./gradlew previewReplacements`
2. 确认替换内容无误后执行：`./gradlew replaceStrings`
3. 查看生成的报告文件

### 4.3 回滚操作
1. 运行回滚任务：`./gradlew rollbackReplacements`
2. 或手动恢复备份文件

## 5. 输出报告格式

### 5.1 报告内容
- 总替换次数统计
- 影响文件数量
- 详细替换信息：文件路径、类名、行号、替换内容
- 执行时间和性能统计

### 5.2 报告格式
- HTML格式：便于浏览和查看
- JSON格式：便于程序化处理
- 支持自定义报告模板
