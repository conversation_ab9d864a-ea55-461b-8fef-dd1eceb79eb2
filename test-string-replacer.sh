#!/bin/bash

# Gradle字符串替换工具测试脚本

echo "=== Gradle字符串替换工具测试 ==="
echo ""

# 检查是否在正确的目录
if [ ! -f "build.gradle" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查Gradle是否可用
if ! command -v ./gradlew &> /dev/null; then
    echo "错误: gradlew不可用，请确保在Android项目根目录"
    exit 1
fi

echo "1. 显示帮助信息..."
./gradlew showStringReplacerHelp
echo ""

echo "2. 生成配置文件..."
./gradlew generateReplacementConfig
echo ""

echo "3. 检查配置文件是否生成..."
if [ -f "string-replacer/replacement-config.json" ]; then
    echo "✅ 配置文件已生成"
    echo "配置文件内容预览:"
    head -10 string-replacer/replacement-config.json
else
    echo "❌ 配置文件生成失败"
    exit 1
fi
echo ""

echo "4. 预览替换操作..."
./gradlew previewReplacements
echo ""

echo "5. 询问是否执行实际替换..."
read -p "是否执行实际的字符串替换? (y/N): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "执行字符串替换..."
    ./gradlew replaceStrings
    echo ""
    
    echo "6. 检查报告文件..."
    if [ -f "string-replacer/replacement-report.html" ]; then
        echo "✅ HTML报告已生成: string-replacer/replacement-report.html"
    fi
    
    if [ -f "string-replacer/replacement-report.json" ]; then
        echo "✅ JSON报告已生成: string-replacer/replacement-report.json"
    fi
    echo ""
    
    echo "7. 询问是否回滚..."
    read -p "是否回滚替换操作? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "回滚替换操作..."
        ./gradlew rollbackReplacements
        echo ""
    fi
else
    echo "跳过实际替换操作"
fi

echo "=== 测试完成 ==="
echo ""
echo "可用的Gradle任务:"
echo "  ./gradlew generateReplacementConfig  # 生成配置文件"
echo "  ./gradlew previewReplacements        # 预览替换"
echo "  ./gradlew replaceStrings             # 执行替换"
echo "  ./gradlew rollbackReplacements       # 回滚替换"
echo "  ./gradlew showStringReplacerHelp     # 显示帮助"
