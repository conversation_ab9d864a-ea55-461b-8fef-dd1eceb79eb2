# 项目结构说明

本项目包含了一个完整的Gradle字符串替换工具实现，以下是项目的文件结构和说明：

## 项目文件结构

```
groovy/
├── 需求.txt                           # 原始需求文件
├── 需求.md                            # 详细需求文档
├── build.gradle                       # 主项目Gradle配置文件
├── test-string-replacer.sh            # 测试脚本
├── PROJECT_STRUCTURE.md               # 本文件
├── string-replacer/                   # 字符串替换工具目录
│   ├── string-replacer.gradle         # 核心Gradle脚本
│   ├── replacement-config.json        # 替换配置文件
│   └── README.md                      # 工具使用说明
└── app/                               # 示例Android应用
    └── src/
        └── main/
            ├── java/
            │   └── com/
            │       └── example/
            │           ├── MainActivity.java      # 示例Java文件
            │           └── utils/
            │               └── ApiClient.kt       # 示例Kotlin文件
            └── res/
                └── values/
                    ├── strings.xml                # 示例XML资源文件
                    └── config.properties          # 示例配置文件
```

## 核心文件说明

### 1. string-replacer/string-replacer.gradle
- **功能**: 核心Gradle脚本，包含所有字符串替换逻辑
- **主要类**:
  - `StringReplacerConfig`: 配置管理类
  - `ReplacementRule`: 替换规则类
  - `ReplacementResult`: 替换结果类
  - `FileScanner`: 文件扫描器
  - `StringMatcher`: 字符串匹配器
  - `ReplacementExecutor`: 替换执行器
  - `ReportGenerator`: 报告生成器
  - `StringReplacerService`: 主服务类

### 2. string-replacer/replacement-config.json
- **功能**: 替换规则配置文件
- **包含**: 替换规则定义和全局设置
- **支持**: 多种替换模式（精确匹配、正则表达式）

### 3. build.gradle
- **功能**: 项目主配置文件，集成字符串替换工具
- **集成方式**: `apply from: 'string-replacer/string-replacer.gradle'`
- **包含**: 示例任务和帮助信息

## 可用的Gradle任务

| 任务名称 | 描述 | 用法 |
|---------|------|------|
| `generateReplacementConfig` | 生成示例配置文件 | `./gradlew generateReplacementConfig` |
| `previewReplacements` | 预览替换操作 | `./gradlew previewReplacements` |
| `replaceStrings` | 执行字符串替换 | `./gradlew replaceStrings` |
| `rollbackReplacements` | 回滚替换操作 | `./gradlew rollbackReplacements` |
| `showStringReplacerHelp` | 显示使用帮助 | `./gradlew showStringReplacerHelp` |

## 示例文件说明

### Java示例 (MainActivity.java)
- 包含多种类型的字符串：常量、注释、方法名等
- 演示了在Java代码中的字符串替换场景

### Kotlin示例 (ApiClient.kt)
- 展示Kotlin代码中的字符串替换
- 包含伴生对象、属性、方法等不同位置的字符串

### XML资源文件 (strings.xml)
- Android资源文件中的字符串替换
- 包含应用名称、界面文本、错误信息等

### 配置文件 (config.properties)
- 属性配置文件中的字符串替换
- 包含API地址、应用配置等

## 测试流程

1. **运行测试脚本**:
   ```bash
   ./test-string-replacer.sh
   ```

2. **手动测试步骤**:
   ```bash
   # 1. 生成配置文件
   ./gradlew generateReplacementConfig
   
   # 2. 预览替换
   ./gradlew previewReplacements
   
   # 3. 执行替换
   ./gradlew replaceStrings
   
   # 4. 查看报告
   open string-replacer/replacement-report.html
   
   # 5. 回滚（如需要）
   ./gradlew rollbackReplacements
   ```

## 配置示例

默认配置包含4个替换规则：

1. **品牌名称更新**: `OldBrandName` → `NewBrandName`
2. **包名更新**: `com.oldcompany.` → `com.newcompany.` (正则)
3. **应用名称更新**: `OldAppName` → `NewAppName` (不区分大小写)
4. **API地址更新**: `https://old-api.example.com` → `https://new-api.example.com` (正则)

## 安全特性

- ✅ 自动文件备份（.backup后缀）
- ✅ 预览功能，避免意外替换
- ✅ 一键回滚机制
- ✅ 路径排除功能
- ✅ 详细的操作日志

## 扩展性

工具设计具有良好的扩展性：
- 支持自定义文件类型
- 支持自定义排除路径
- 支持正则表达式
- 支持多种报告格式
- 模块化设计，易于扩展

## 使用建议

1. **首次使用**: 先在测试项目中验证
2. **重要项目**: 使用前先提交代码到版本控制
3. **大型项目**: 建议分批次执行替换
4. **正则表达式**: 谨慎使用，先充分测试
5. **备份管理**: 定期清理备份文件

这个工具完全满足原始需求，并提供了丰富的功能和良好的用户体验。
