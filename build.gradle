// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = "1.8.0"
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:7.4.0"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

plugins {
    id 'com.android.application' version '7.4.0' apply false
    id 'com.android.library' version '7.4.0' apply false
    id 'org.jetbrains.kotlin.android' version '1.8.0' apply false
}

// 应用字符串替换工具（独立版本，避免版本冲突）
apply from: 'string-replacer/standalone-string-replacer.gradle'

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

// 自定义任务示例
task showStringReplacerHelp {
    group 'help'
    description '显示字符串替换工具的使用帮助'
    
    doLast {
        println """
=== Gradle字符串替换工具使用指南 ===

1. 生成配置文件:
   ./gradlew generateReplacementConfig

2. 编辑配置文件:
   修改 string-replacer/replacement-config.json 文件，配置您的替换规则

3. 预览替换操作:
   ./gradlew previewReplacements

4. 执行替换操作:
   ./gradlew replaceStrings

5. 回滚替换操作:
   ./gradlew rollbackReplacements

配置文件说明:
- searchPattern: 要搜索的字符串或正则表达式
- replaceWith: 替换后的字符串
- caseSensitive: 是否区分大小写
- useRegex: 是否使用正则表达式
- fileTypes: 要处理的文件类型
- excludePaths: 要排除的路径

注意事项:
- 执行替换前会自动备份原文件
- 可以通过rollbackReplacements任务回滚更改
- 替换完成后会生成详细的报告文件
"""
    }
}
