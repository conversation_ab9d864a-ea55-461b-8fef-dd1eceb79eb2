/**
 * 独立的Gradle字符串替换工具
 * 不依赖于项目的构建配置，避免版本冲突
 */

import groovy.json.JsonSlurper
import groovy.json.JsonBuilder
import java.util.regex.Pattern
import java.text.SimpleDateFormat

// 配置类
class StringReplacerConfig {
    String configFile = 'string-replacer/replacement-config.json'
    String reportFormat = 'html'
    boolean backupEnabled = true
    String backupSuffix = '.backup'
    String logLevel = 'info'
    List<String> defaultFileTypes = ['java', 'kt', 'xml', 'properties']
    List<String> defaultExcludePaths = ['build/', '.gradle/', '.git/']
}

// 替换规则类
class ReplacementRule {
    String id
    String description
    String searchPattern
    String replaceWith
    boolean caseSensitive = true
    boolean useRegex = false
    List<String> fileTypes = []
    List<String> excludePaths = []
}

// 替换结果类
class ReplacementResult {
    String filePath
    String className
    int lineNumber
    int columnNumber
    String originalText
    String replacedText
    String context
    String ruleId
}

// 独立的字符串替换服务
class StandaloneStringReplacer {
    private File projectDir
    private StringReplacerConfig config
    
    StandaloneStringReplacer(File projectDir) {
        this.projectDir = projectDir
        this.config = new StringReplacerConfig()
    }
    
    List<ReplacementRule> loadConfiguration() {
        File configFile = new File(projectDir, config.configFile)
        if (!configFile.exists()) {
            throw new RuntimeException("配置文件不存在: ${configFile.path}")
        }
        
        try {
            def jsonSlurper = new JsonSlurper()
            def configData = jsonSlurper.parse(configFile)
            
            // 加载全局设置
            if (configData.globalSettings) {
                def globalSettings = configData.globalSettings
                config.backupEnabled = globalSettings.createBackup ?: true
                config.backupSuffix = globalSettings.backupSuffix ?: '.backup'
                config.reportFormat = globalSettings.reportFormat ?: 'html'
                config.logLevel = globalSettings.logLevel ?: 'info'
            }
            
            // 加载替换规则
            List<ReplacementRule> rules = []
            configData.replacements?.each { ruleData ->
                ReplacementRule rule = new ReplacementRule(
                    id: ruleData.id,
                    description: ruleData.description,
                    searchPattern: ruleData.searchPattern,
                    replaceWith: ruleData.replaceWith,
                    caseSensitive: ruleData.caseSensitive ?: true,
                    useRegex: ruleData.useRegex ?: false,
                    fileTypes: ruleData.fileTypes ?: config.defaultFileTypes,
                    excludePaths: ruleData.excludePaths ?: config.defaultExcludePaths
                )
                rules.add(rule)
            }
            
            return rules
        } catch (Exception e) {
            throw new RuntimeException("解析配置文件失败: ${e.message}")
        }
    }
    
    List<File> scanFiles(List<String> fileTypes, List<String> excludePaths) {
        List<File> files = []
        scanDirectory(projectDir, files, fileTypes, excludePaths)
        return files
    }
    
    private void scanDirectory(File dir, List<File> files, List<String> fileTypes, List<String> excludePaths) {
        if (!dir.exists() || !dir.isDirectory()) return
        
        dir.eachFileRecurse { file ->
            if (file.isFile() && shouldIncludeFile(file, fileTypes, excludePaths)) {
                files.add(file)
            }
        }
    }
    
    private boolean shouldIncludeFile(File file, List<String> fileTypes, List<String> excludePaths) {
        String relativePath = projectDir.toPath().relativize(file.toPath()).toString()
        
        // 检查是否在排除路径中
        for (String excludePath : excludePaths) {
            if (relativePath.contains(excludePath)) {
                return false
            }
        }
        
        // 检查文件类型
        String extension = getFileExtension(file.name)
        return fileTypes.contains(extension)
    }
    
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.')
        return lastDot > 0 ? fileName.substring(lastDot + 1) : ''
    }
    
    List<ReplacementResult> findMatches(File file, ReplacementRule rule) {
        List<ReplacementResult> results = []
        
        try {
            List<String> lines = file.readLines('UTF-8')
            
            for (int i = 0; i < lines.size(); i++) {
                String line = lines[i]
                List<ReplacementResult> lineResults = findMatchesInLine(file, line, i + 1, rule)
                results.addAll(lineResults)
            }
        } catch (Exception e) {
            println "Error reading file ${file.path}: ${e.message}"
        }
        
        return results
    }
    
    private List<ReplacementResult> findMatchesInLine(File file, String line, int lineNumber, ReplacementRule rule) {
        List<ReplacementResult> results = []
        
        if (rule.useRegex) {
            Pattern pattern = rule.caseSensitive ? 
                Pattern.compile(rule.searchPattern) : 
                Pattern.compile(rule.searchPattern, Pattern.CASE_INSENSITIVE)
            
            def matcher = pattern.matcher(line)
            while (matcher.find()) {
                ReplacementResult result = new ReplacementResult(
                    filePath: getRelativePath(file),
                    className: extractClassName(file),
                    lineNumber: lineNumber,
                    columnNumber: matcher.start() + 1,
                    originalText: matcher.group(),
                    replacedText: rule.replaceWith,
                    context: line.trim(),
                    ruleId: rule.id
                )
                results.add(result)
            }
        } else {
            String searchText = rule.caseSensitive ? rule.searchPattern : rule.searchPattern.toLowerCase()
            String lineText = rule.caseSensitive ? line : line.toLowerCase()
            
            int index = lineText.indexOf(searchText)
            while (index != -1) {
                ReplacementResult result = new ReplacementResult(
                    filePath: getRelativePath(file),
                    className: extractClassName(file),
                    lineNumber: lineNumber,
                    columnNumber: index + 1,
                    originalText: line.substring(index, index + rule.searchPattern.length()),
                    replacedText: rule.replaceWith,
                    context: line.trim(),
                    ruleId: rule.id
                )
                results.add(result)
                index = lineText.indexOf(searchText, index + 1)
            }
        }
        
        return results
    }
    
    private String getRelativePath(File file) {
        return projectDir.toPath().relativize(file.toPath()).toString()
    }
    
    private String extractClassName(File file) {
        String fileName = file.name
        int lastDot = fileName.lastIndexOf('.')
        return lastDot > 0 ? fileName.substring(0, lastDot) : fileName
    }
    
    List<ReplacementResult> previewReplacements() {
        List<ReplacementRule> rules = loadConfiguration()
        List<ReplacementResult> allMatches = []
        
        rules.each { rule ->
            println "正在搜索规则: ${rule.id} - ${rule.description}"
            
            List<File> files = scanFiles(rule.fileTypes, rule.excludePaths)
            println "扫描到 ${files.size()} 个文件"
            
            files.each { file ->
                List<ReplacementResult> matches = findMatches(file, rule)
                allMatches.addAll(matches)
            }
        }
        
        println "总共找到 ${allMatches.size()} 个匹配项"
        return allMatches
    }
    
    List<ReplacementResult> executeReplacements() {
        List<ReplacementRule> rules = loadConfiguration()
        List<ReplacementResult> allMatches = previewReplacements()
        
        if (allMatches.isEmpty()) {
            println "没有找到需要替换的内容"
            return []
        }
        
        println "开始执行替换操作..."
        
        // 按文件分组执行替换
        Map<String, List<ReplacementResult>> fileGroups = allMatches.groupBy { it.filePath }
        List<ReplacementResult> executedReplacements = []
        
        fileGroups.each { filePath, fileMatches ->
            File file = new File(projectDir, filePath)
            if (file.exists()) {
                List<ReplacementResult> fileReplacements = executeFileReplacements(file, fileMatches, rules)
                executedReplacements.addAll(fileReplacements)
            }
        }
        
        println "替换完成，共执行 ${executedReplacements.size()} 次替换"
        
        // 生成报告
        generateReport(executedReplacements, rules)
        
        return executedReplacements
    }
    
    private List<ReplacementResult> executeFileReplacements(File file, List<ReplacementResult> matches, List<ReplacementRule> rules) {
        List<ReplacementResult> executedReplacements = []
        
        try {
            // 备份文件
            if (config.backupEnabled) {
                createBackup(file)
            }
            
            // 读取文件内容
            String content = file.getText('UTF-8')
            String originalContent = content
            
            // 按规则执行替换
            for (ReplacementRule rule : rules) {
                if (rule.useRegex) {
                    Pattern pattern = rule.caseSensitive ? 
                        Pattern.compile(rule.searchPattern) : 
                        Pattern.compile(rule.searchPattern, Pattern.CASE_INSENSITIVE)
                    content = content.replaceAll(pattern.pattern(), rule.replaceWith)
                } else {
                    if (rule.caseSensitive) {
                        content = content.replace(rule.searchPattern, rule.replaceWith)
                    } else {
                        content = content.replaceAll("(?i)" + Pattern.quote(rule.searchPattern), rule.replaceWith)
                    }
                }
            }
            
            // 如果内容有变化，写入文件
            if (content != originalContent) {
                file.setText(content, 'UTF-8')
                
                // 记录实际执行的替换
                matches.each { match ->
                    if (shouldExecuteReplacement(match, rules)) {
                        executedReplacements.add(match)
                    }
                }
            }
            
        } catch (Exception e) {
            println "Error processing file ${file.path}: ${e.message}"
        }
        
        return executedReplacements
    }
    
    private void createBackup(File file) {
        File backupFile = new File(file.path + config.backupSuffix)
        backupFile.text = file.text
    }
    
    private boolean shouldExecuteReplacement(ReplacementResult match, List<ReplacementRule> rules) {
        return rules.any { rule -> rule.id == match.ruleId }
    }
    
    void generateReport(List<ReplacementResult> results, List<ReplacementRule> rules) {
        if (config.reportFormat == 'html') {
            generateHtmlReport(results, rules)
        } else if (config.reportFormat == 'json') {
            generateJsonReport(results, rules)
        }
    }
    
    private void generateHtmlReport(List<ReplacementResult> results, List<ReplacementRule> rules) {
        String timestamp = new SimpleDateFormat('yyyy-MM-dd HH:mm:ss').format(new Date())
        File reportFile = new File(projectDir, 'string-replacer/replacement-report.html')
        
        String html = """
<!DOCTYPE html>
<html>
<head>
    <title>字符串替换报告</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .context { font-family: monospace; background: #f9f9f9; padding: 5px; }
    </style>
</head>
<body>
    <h1>字符串替换报告</h1>
    <p>生成时间: ${timestamp}</p>
    
    <div class="summary">
        <h2>执行摘要</h2>
        <ul>
            <li>总替换次数: ${results.size()}</li>
            <li>影响文件数: ${results.collect { it.filePath }.unique().size()}</li>
            <li>使用规则数: ${rules.size()}</li>
        </ul>
    </div>
    
    <h2>详细替换信息</h2>
    <table>
        <thead>
            <tr>
                <th>文件路径</th>
                <th>类名</th>
                <th>行号</th>
                <th>替换前</th>
                <th>替换后</th>
                <th>上下文</th>
                <th>规则ID</th>
            </tr>
        </thead>
        <tbody>
"""
        
        results.each { result ->
            html += """
            <tr>
                <td>${result.filePath}</td>
                <td>${result.className}</td>
                <td>${result.lineNumber}</td>
                <td>${result.originalText}</td>
                <td>${result.replacedText}</td>
                <td class="context">${result.context}</td>
                <td>${result.ruleId}</td>
            </tr>
"""
        }
        
        html += """
        </tbody>
    </table>
</body>
</html>
"""
        
        reportFile.parentFile.mkdirs()
        reportFile.text = html
        println "HTML报告已生成: ${reportFile.path}"
    }
    
    private void generateJsonReport(List<ReplacementResult> results, List<ReplacementRule> rules) {
        String timestamp = new SimpleDateFormat('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'').format(new Date())
        File reportFile = new File(projectDir, 'string-replacer/replacement-report.json')
        
        def reportData = [
            executionSummary: [
                timestamp: timestamp,
                totalReplacements: results.size(),
                affectedFiles: results.collect { it.filePath }.unique().size(),
                rulesUsed: rules.size()
            ],
            replacementDetails: results.collect { result ->
                [
                    filePath: result.filePath,
                    className: result.className,
                    lineNumber: result.lineNumber,
                    columnNumber: result.columnNumber,
                    originalText: result.originalText,
                    replacedText: result.replacedText,
                    context: result.context,
                    ruleId: result.ruleId
                ]
            },
            rules: rules.collect { rule ->
                [
                    id: rule.id,
                    description: rule.description,
                    searchPattern: rule.searchPattern,
                    replaceWith: rule.replaceWith,
                    caseSensitive: rule.caseSensitive,
                    useRegex: rule.useRegex
                ]
            }
        ]
        
        reportFile.parentFile.mkdirs()
        reportFile.text = new JsonBuilder(reportData).toPrettyString()
        println "JSON报告已生成: ${reportFile.path}"
    }
}

// 创建独立的任务
def standaloneReplacer = new StandaloneStringReplacer(project.projectDir)

task standalonePreviewReplacements {
    group 'string-replacer'
    description '独立预览字符串替换操作（避免版本冲突）'
    
    doLast {
        try {
            List<ReplacementResult> matches = standaloneReplacer.previewReplacements()
            
            if (matches.isEmpty()) {
                println "没有找到匹配的字符串"
            } else {
                println "\n=== 预览结果 ==="
                matches.each { match ->
                    println "${match.filePath}:${match.lineNumber} - ${match.originalText} -> ${match.replacedText}"
                }
                println "\n总共找到 ${matches.size()} 个匹配项"
                println "运行 './gradlew standaloneReplaceStrings' 执行替换"
            }
        } catch (Exception e) {
            println "预览失败: ${e.message}"
            e.printStackTrace()
        }
    }
}

task standaloneReplaceStrings {
    group 'string-replacer'
    description '独立执行字符串替换操作（避免版本冲突）'
    
    doLast {
        try {
            List<ReplacementResult> results = standaloneReplacer.executeReplacements()
            
            if (results.isEmpty()) {
                println "没有执行任何替换操作"
            } else {
                println "\n=== 替换完成 ==="
                println "共执行 ${results.size()} 次替换"
                println "影响文件数: ${results.collect { it.filePath }.unique().size()}"
                println "报告文件已生成在 string-replacer/ 目录下"
            }
        } catch (Exception e) {
            println "替换失败: ${e.message}"
            e.printStackTrace()
        }
    }
}

println "独立字符串替换工具已加载"
println "可用任务:"
println "  - standalonePreviewReplacements: 预览替换操作"
println "  - standaloneReplaceStrings: 执行替换操作"
