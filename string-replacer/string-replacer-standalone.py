#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
独立的字符串替换工具
不依赖于Gradle，直接使用Python实现
解决Java版本兼容性问题
"""

import os
import json
import re
import shutil
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Tuple

class StringReplacer:
    def __init__(self, project_dir: str):
        self.project_dir = Path(project_dir)
        self.config_file = self.project_dir / "string-replacer" / "replacement-config.json"
        self.config = {}
        self.results = []
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            return self.config
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件JSON格式错误: {e}")
    
    def should_include_file(self, file_path: Path, file_types: List[str], exclude_paths: List[str]) -> bool:
        """判断是否应该包含该文件"""
        # 获取相对路径
        try:
            relative_path = file_path.relative_to(self.project_dir)
        except ValueError:
            return False
        
        relative_path_str = str(relative_path)
        
        # 检查排除路径
        for exclude_path in exclude_paths:
            if exclude_path in relative_path_str:
                return False
        
        # 检查文件类型
        file_extension = file_path.suffix[1:] if file_path.suffix else ''
        return file_extension in file_types
    
    def scan_files(self, file_types: List[str], exclude_paths: List[str]) -> List[Path]:
        """扫描文件"""
        files = []
        
        for file_path in self.project_dir.rglob('*'):
            if file_path.is_file() and self.should_include_file(file_path, file_types, exclude_paths):
                files.append(file_path)
        
        return files
    
    def find_matches_in_file(self, file_path: Path, rule: Dict[str, Any]) -> List[Dict[str, Any]]:
        """在文件中查找匹配项"""
        matches = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except (UnicodeDecodeError, PermissionError) as e:
            print(f"警告: 无法读取文件 {file_path}: {e}")
            return matches
        
        search_pattern = rule['searchPattern']
        replace_with = rule['replaceWith']
        case_sensitive = rule.get('caseSensitive', True)
        use_regex = rule.get('useRegex', False)
        
        for line_num, line in enumerate(lines, 1):
            line_matches = []
            
            if use_regex:
                flags = 0 if case_sensitive else re.IGNORECASE
                try:
                    pattern = re.compile(search_pattern, flags)
                    for match in pattern.finditer(line):
                        line_matches.append({
                            'start': match.start(),
                            'end': match.end(),
                            'matched_text': match.group()
                        })
                except re.error as e:
                    print(f"警告: 正则表达式错误 '{search_pattern}': {e}")
                    continue
            else:
                search_text = search_pattern if case_sensitive else search_pattern.lower()
                line_text = line if case_sensitive else line.lower()
                
                start = 0
                while True:
                    pos = line_text.find(search_text, start)
                    if pos == -1:
                        break
                    line_matches.append({
                        'start': pos,
                        'end': pos + len(search_pattern),
                        'matched_text': line[pos:pos + len(search_pattern)]
                    })
                    start = pos + 1
            
            # 记录匹配结果
            for match in line_matches:
                relative_path = file_path.relative_to(self.project_dir)
                class_name = file_path.stem
                
                matches.append({
                    'filePath': str(relative_path),
                    'className': class_name,
                    'lineNumber': line_num,
                    'columnNumber': match['start'] + 1,
                    'originalText': match['matched_text'],
                    'replacedText': replace_with,
                    'context': line.strip(),
                    'ruleId': rule['id']
                })
        
        return matches
    
    def preview_replacements(self) -> List[Dict[str, Any]]:
        """预览替换操作"""
        config = self.load_config()
        all_matches = []
        
        for rule in config.get('replacements', []):
            print(f"正在搜索规则: {rule['id']} - {rule['description']}")
            
            file_types = rule.get('fileTypes', ['java', 'kt', 'xml', 'properties'])
            exclude_paths = rule.get('excludePaths', ['build/', '.gradle/', '.git/'])
            
            files = self.scan_files(file_types, exclude_paths)
            print(f"扫描到 {len(files)} 个文件")
            
            for file_path in files:
                matches = self.find_matches_in_file(file_path, rule)
                all_matches.extend(matches)
        
        print(f"总共找到 {len(all_matches)} 个匹配项")
        return all_matches
    
    def execute_replacements(self) -> List[Dict[str, Any]]:
        """执行替换操作"""
        config = self.load_config()
        global_settings = config.get('globalSettings', {})
        create_backup = global_settings.get('createBackup', True)
        backup_suffix = global_settings.get('backupSuffix', '.backup')
        
        # 先预览获取所有匹配项
        all_matches = self.preview_replacements()
        
        if not all_matches:
            print("没有找到需要替换的内容")
            return []
        
        print("开始执行替换操作...")
        
        # 按文件分组
        files_to_process = {}
        for match in all_matches:
            file_path = match['filePath']
            if file_path not in files_to_process:
                files_to_process[file_path] = []
            files_to_process[file_path].append(match)
        
        executed_replacements = []
        
        for relative_path, file_matches in files_to_process.items():
            file_path = self.project_dir / relative_path
            
            if not file_path.exists():
                continue
            
            # 备份文件
            if create_backup:
                backup_path = Path(str(file_path) + backup_suffix)
                shutil.copy2(file_path, backup_path)
                print(f"已备份: {backup_path}")
            
            # 读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                original_content = content
                
                # 执行替换
                for rule in config.get('replacements', []):
                    search_pattern = rule['searchPattern']
                    replace_with = rule['replaceWith']
                    case_sensitive = rule.get('caseSensitive', True)
                    use_regex = rule.get('useRegex', False)
                    
                    if use_regex:
                        flags = 0 if case_sensitive else re.IGNORECASE
                        try:
                            content = re.sub(search_pattern, replace_with, content, flags=flags)
                        except re.error as e:
                            print(f"警告: 正则表达式替换错误 '{search_pattern}': {e}")
                    else:
                        if case_sensitive:
                            content = content.replace(search_pattern, replace_with)
                        else:
                            # 不区分大小写的替换
                            pattern = re.compile(re.escape(search_pattern), re.IGNORECASE)
                            content = pattern.sub(replace_with, content)
                
                # 写入文件
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    # 记录执行的替换
                    executed_replacements.extend(file_matches)
                    print(f"已处理: {relative_path}")
                
            except (UnicodeDecodeError, PermissionError) as e:
                print(f"错误: 无法处理文件 {file_path}: {e}")
        
        print(f"替换完成，共执行 {len(executed_replacements)} 次替换")
        
        # 生成报告
        self.generate_report(executed_replacements, config.get('replacements', []))
        
        return executed_replacements
    
    def generate_report(self, results: List[Dict[str, Any]], rules: List[Dict[str, Any]]):
        """生成报告"""
        config = self.load_config()
        global_settings = config.get('globalSettings', {})
        report_format = global_settings.get('reportFormat', 'html')
        
        if report_format == 'html':
            self.generate_html_report(results, rules)
        elif report_format == 'json':
            self.generate_json_report(results, rules)
    
    def generate_html_report(self, results: List[Dict[str, Any]], rules: List[Dict[str, Any]]):
        """生成HTML报告"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        report_file = self.project_dir / "string-replacer" / "replacement-report.html"
        
        unique_files = len(set(result['filePath'] for result in results))
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>字符串替换报告</title>
    <meta charset="UTF-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .summary {{ background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .context {{ font-family: monospace; background: #f9f9f9; padding: 5px; }}
    </style>
</head>
<body>
    <h1>字符串替换报告</h1>
    <p>生成时间: {timestamp}</p>
    
    <div class="summary">
        <h2>执行摘要</h2>
        <ul>
            <li>总替换次数: {len(results)}</li>
            <li>影响文件数: {unique_files}</li>
            <li>使用规则数: {len(rules)}</li>
        </ul>
    </div>
    
    <h2>详细替换信息</h2>
    <table>
        <thead>
            <tr>
                <th>文件路径</th>
                <th>类名</th>
                <th>行号</th>
                <th>替换前</th>
                <th>替换后</th>
                <th>上下文</th>
                <th>规则ID</th>
            </tr>
        </thead>
        <tbody>
"""
        
        for result in results:
            html_content += f"""
            <tr>
                <td>{result['filePath']}</td>
                <td>{result['className']}</td>
                <td>{result['lineNumber']}</td>
                <td>{result['originalText']}</td>
                <td>{result['replacedText']}</td>
                <td class="context">{result['context']}</td>
                <td>{result['ruleId']}</td>
            </tr>
"""
        
        html_content += """
        </tbody>
    </table>
</body>
</html>
"""
        
        report_file.parent.mkdir(exist_ok=True)
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML报告已生成: {report_file}")
    
    def generate_json_report(self, results: List[Dict[str, Any]], rules: List[Dict[str, Any]]):
        """生成JSON报告"""
        timestamp = datetime.now().isoformat()
        report_file = self.project_dir / "string-replacer" / "replacement-report.json"
        
        unique_files = len(set(result['filePath'] for result in results))
        
        report_data = {
            'executionSummary': {
                'timestamp': timestamp,
                'totalReplacements': len(results),
                'affectedFiles': unique_files,
                'rulesUsed': len(rules)
            },
            'replacementDetails': results,
            'rules': rules
        }
        
        report_file.parent.mkdir(exist_ok=True)
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"JSON报告已生成: {report_file}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 string-replacer-standalone.py <command>")
        print("命令:")
        print("  preview  - 预览替换操作")
        print("  execute  - 执行替换操作")
        sys.exit(1)
    
    command = sys.argv[1]
    project_dir = os.getcwd()
    
    replacer = StringReplacer(project_dir)
    
    try:
        if command == 'preview':
            print("=== 预览替换操作 ===")
            matches = replacer.preview_replacements()
            
            if matches:
                print("\n=== 预览结果 ===")
                for match in matches[:10]:  # 只显示前10个
                    print(f"{match['filePath']}:{match['lineNumber']} - {match['originalText']} -> {match['replacedText']}")
                
                if len(matches) > 10:
                    print(f"... 还有 {len(matches) - 10} 个匹配项")
                
                print(f"\n总共找到 {len(matches)} 个匹配项")
                print("运行 'python3 string-replacer-standalone.py execute' 执行替换")
            else:
                print("没有找到匹配的字符串")
        
        elif command == 'execute':
            print("=== 执行字符串替换 ===")
            print("⚠️  警告: 即将执行实际的字符串替换操作")
            print("建议先备份您的代码到版本控制系统")
            
            confirm = input("确认执行替换操作? (y/N): ").strip().lower()
            if confirm == 'y':
                results = replacer.execute_replacements()
                
                if results:
                    unique_files = len(set(result['filePath'] for result in results))
                    print(f"\n=== 替换完成 ===")
                    print(f"共执行 {len(results)} 次替换")
                    print(f"影响文件数: {unique_files}")
                    print("报告文件已生成在 string-replacer/ 目录下")
                else:
                    print("没有执行任何替换操作")
            else:
                print("操作已取消")
        
        else:
            print(f"未知命令: {command}")
            sys.exit(1)
    
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
