{"replacements": [{"id": "brand-name-update", "description": "更新品牌名称", "searchPattern": "OldBrandName", "replaceWith": "NewBrandName", "caseSensitive": true, "useRegex": false, "fileTypes": ["java", "kt", "xml", "properties"], "excludePaths": ["build/", "test/", ".gradle/", ".git/"]}, {"id": "package-name-update", "description": "更新包名", "searchPattern": "com\\.oldcompany\\.", "replaceWith": "com.newcompany.", "caseSensitive": true, "useRegex": true, "fileTypes": ["java", "kt", "xml"], "excludePaths": ["build/", "test/"]}, {"id": "app-name-update", "description": "更新应用名称", "searchPattern": "OldAppName", "replaceWith": "NewAppName", "caseSensitive": false, "useRegex": false, "fileTypes": ["xml", "properties"], "excludePaths": ["build/"]}, {"id": "url-update", "description": "更新API地址", "searchPattern": "https://old-api\\.example\\.com", "replaceWith": "https://new-api.example.com", "caseSensitive": true, "useRegex": true, "fileTypes": ["java", "kt", "properties"], "excludePaths": ["build/", "test/"]}], "globalSettings": {"createBackup": true, "backupSuffix": ".backup", "reportFormat": "html", "logLevel": "info"}}