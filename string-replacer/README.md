# Gradle字符串替换工具

一个强大的Gradle脚本工具，用于在Android项目中批量搜索和替换字符串，支持多模块项目和详细的替换报告。

## 功能特性

- ✅ 支持多模块Android项目
- ✅ 配置化的替换规则
- ✅ 支持正则表达式匹配
- ✅ 自动文件备份
- ✅ 详细的替换报告（HTML/JSON格式）
- ✅ 一键回滚功能
- ✅ 灵活的文件类型过滤
- ✅ 路径排除功能

## 快速开始

### 1. 集成到项目

将 `string-replacer` 目录复制到您的Android项目根目录，然后在项目的 `build.gradle` 文件中添加：

```gradle
// 应用字符串替换工具
apply from: 'string-replacer/string-replacer.gradle'
```

### 2. 生成配置文件

```bash
./gradlew generateReplacementConfig
```

这将在 `string-replacer/replacement-config.json` 生成示例配置文件。

### 3. 配置替换规则

编辑 `string-replacer/replacement-config.json` 文件，配置您的替换规则：

```json
{
  "replacements": [
    {
      "id": "brand-name-update",
      "description": "更新品牌名称",
      "searchPattern": "OldBrandName",
      "replaceWith": "NewBrandName",
      "caseSensitive": true,
      "useRegex": false,
      "fileTypes": ["java", "kt", "xml"],
      "excludePaths": ["build/", "test/"]
    }
  ],
  "globalSettings": {
    "createBackup": true,
    "backupSuffix": ".backup",
    "reportFormat": "html",
    "logLevel": "info"
  }
}
```

### 4. 预览替换操作

```bash
./gradlew previewReplacements
```

这将显示所有匹配的字符串，但不执行实际替换。

### 5. 执行替换操作

```bash
./gradlew replaceStrings
```

执行实际的字符串替换，并生成详细报告。

### 6. 回滚操作（如需要）

```bash
./gradlew rollbackReplacements
```

## 可用任务

| 任务名称 | 描述 |
|---------|------|
| `generateReplacementConfig` | 生成示例配置文件 |
| `previewReplacements` | 预览替换操作，不执行实际替换 |
| `replaceStrings` | 执行字符串替换操作 |
| `rollbackReplacements` | 回滚字符串替换操作 |

## 配置文件详解

### 替换规则配置

每个替换规则包含以下字段：

- `id`: 规则的唯一标识符
- `description`: 规则描述
- `searchPattern`: 要搜索的字符串或正则表达式
- `replaceWith`: 替换后的字符串
- `caseSensitive`: 是否区分大小写（默认：true）
- `useRegex`: 是否使用正则表达式（默认：false）
- `fileTypes`: 要处理的文件类型数组（如：["java", "kt", "xml"]）
- `excludePaths`: 要排除的路径数组（如：["build/", "test/"]）

### 全局设置

- `createBackup`: 是否创建备份文件（默认：true）
- `backupSuffix`: 备份文件后缀（默认：".backup"）
- `reportFormat`: 报告格式，"html" 或 "json"（默认："html"）
- `logLevel`: 日志级别（默认："info"）

## 正则表达式示例

### 包名替换
```json
{
  "searchPattern": "com\\.oldcompany\\.",
  "replaceWith": "com.newcompany.",
  "useRegex": true
}
```

### URL替换
```json
{
  "searchPattern": "https://old-api\\.example\\.com",
  "replaceWith": "https://new-api.example.com",
  "useRegex": true
}
```

### 版本号替换
```json
{
  "searchPattern": "version\\s*=\\s*\"1\\.0\\.0\"",
  "replaceWith": "version = \"2.0.0\"",
  "useRegex": true
}
```

## 报告文件

执行替换后，工具会生成详细的报告文件：

- **HTML报告**: `string-replacer/replacement-report.html` - 便于浏览查看
- **JSON报告**: `string-replacer/replacement-report.json` - 便于程序化处理

报告包含：
- 执行摘要（总替换次数、影响文件数等）
- 详细替换信息（文件路径、类名、行号、替换内容等）
- 使用的规则信息

## 安全特性

1. **自动备份**: 替换前自动备份原文件
2. **预览功能**: 可以先预览替换结果
3. **回滚机制**: 一键回滚所有更改
4. **路径排除**: 自动排除build目录等临时文件
5. **错误处理**: 完善的错误处理和日志记录

## 注意事项

1. 执行替换前建议先运行预览任务
2. 重要项目建议先提交代码到版本控制系统
3. 正则表达式要谨慎使用，避免意外替换
4. 大型项目建议分批次执行替换
5. 备份文件会占用额外磁盘空间

## 故障排除

### 配置文件解析错误
检查JSON格式是否正确，可以使用在线JSON验证工具。

### 没有找到匹配项
- 检查搜索模式是否正确
- 确认文件类型配置是否包含目标文件
- 检查排除路径是否过于宽泛

### 权限错误
确保Gradle有读写项目文件的权限。

## 许可证

本工具基于MIT许可证开源。
