#!/bin/bash

# 独立的字符串替换工具运行脚本
# 不依赖于项目的Gradle配置，避免版本冲突

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "=== Gradle字符串替换工具（独立版本）==="
echo "项目目录: $PROJECT_DIR"
echo ""

# 检查配置文件是否存在
CONFIG_FILE="$SCRIPT_DIR/replacement-config.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    echo "请先运行: ./gradlew generateReplacementConfig"
    exit 1
fi

echo "✅ 找到配置文件: $CONFIG_FILE"

# 创建临时的Gradle脚本
TEMP_BUILD_FILE="$SCRIPT_DIR/temp-build.gradle"

cat > "$TEMP_BUILD_FILE" << 'EOF'
// 临时构建文件，用于运行字符串替换工具

apply from: 'standalone-string-replacer.gradle'

// 默认任务
defaultTasks 'standalonePreviewReplacements'
EOF

echo "✅ 创建临时构建文件"

# 询问用户要执行的操作
echo ""
echo "请选择要执行的操作:"
echo "1) 预览替换操作（推荐先执行）"
echo "2) 执行字符串替换"
echo "3) 退出"
echo ""

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "=== 预览替换操作 ==="
        cd "$SCRIPT_DIR"
        gradle -b temp-build.gradle standalonePreviewReplacements
        ;;
    2)
        echo ""
        echo "⚠️  警告: 即将执行实际的字符串替换操作"
        echo "建议先备份您的代码到版本控制系统"
        echo ""
        read -p "确认执行替换操作? (y/N): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "=== 执行字符串替换 ==="
            cd "$SCRIPT_DIR"
            gradle -b temp-build.gradle standaloneReplaceStrings
            
            echo ""
            echo "✅ 替换完成！"
            echo "📊 查看报告: open $SCRIPT_DIR/replacement-report.html"
            echo "🔄 如需回滚: 恢复 .backup 文件"
        else
            echo "操作已取消"
        fi
        ;;
    3)
        echo "退出"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 清理临时文件
rm -f "$TEMP_BUILD_FILE"

echo ""
echo "=== 完成 ==="
